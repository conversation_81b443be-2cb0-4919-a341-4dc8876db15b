/* Smart QA - Notion Style CSS */

/* ====== Notion Design System Variables ====== */
:root {
	/* 基础色彩 (Light) - Notion规范 */
	--notion-fg-default: #37352F;
	--notion-fg-muted: rgba(55, 53, 47, 0.6);
	--notion-bg-default: #FFFFFF;
	--notion-border: rgba(55, 53, 47, 0.16);
	--notion-rule: rgba(55, 53, 47, 0.09);
	
	/* Notion强调色 (Light) */
	--notion-gray-bg: #F1F1EF;    --notion-gray-fg: #787774;
	--notion-brown-bg: #F4EEEE;   --notion-brown-fg: #9F6B53;
	--notion-orange-bg: #FAEBDD;  --notion-orange-fg: #D9730D;
	--notion-yellow-bg: #FBF3DB;  --notion-yellow-fg: #B88700;
	--notion-green-bg: #EDF3EC;   --notion-green-fg: #0F7B6C;
	--notion-blue-bg: #E7F3F8;    --notion-blue-fg: #0B6E99;
	--notion-purple-bg: #F6F3F9;  --notion-purple-fg: #6940A5;
	--notion-pink-bg: #FAF1F5;    --notion-pink-fg: #AD1A72;
	--notion-red-bg: #FDEBEC;     --notion-red-fg: #E03E3E;
	
	/* 字体系统 */
	--notion-font-body: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
	--notion-font-mono: "IBM Plex Mono", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
	
	/* 字号与行高 */
	--notion-fs-body: 16px;     --notion-lh-body: 1.6;
	--notion-fs-h1: 30px;       --notion-lh-h1: 1.25;
	--notion-fs-h2: 24px;       --notion-lh-h2: 1.30;
	--notion-fs-h3: 20px;       --notion-lh-h3: 1.40;
	--notion-fs-small: 14px;    /* Small text模式 */
	
	/* 间距系统 (4px网格) */
	--notion-space-1: 4px;
	--notion-space-2: 8px;
	--notion-space-3: 12px;
	--notion-space-4: 16px;
	--notion-space-5: 20px;
	--notion-space-6: 24px;
	
	/* 圆角系统 */
	--notion-radius-1: 3px;
	--notion-radius-2: 6px;
	
	/* 内容宽度 */
	--notion-content-max: 700px;
	
	/* 亮色模式变量 */
	--smart-bg-primary: var(--notion-bg-default);
	--smart-bg-secondary: var(--notion-gray-bg);
	--smart-border: var(--notion-border);
	--smart-hover: var(--notion-gray-bg);
	--smart-text-normal: var(--notion-fg-default);
	--smart-text-muted: var(--notion-fg-muted);
	--smart-text-faint: rgba(55, 53, 47, 0.4);
	--smart-accent: var(--notion-blue-fg);
}

/* 暗色模式覆盖 - 统一使用 Obsidian 深蓝色主题 */
.theme-dark,
.theme-dark :root,
.mod-linux.theme-dark,
.mod-windows.theme-dark,
.mod-macos.theme-dark,
body.theme-dark {
	--smart-bg-primary: var(--background-primary);
	--smart-bg-secondary: var(--background-secondary);
	--smart-border: var(--background-modifier-border);
	--smart-hover: var(--background-modifier-hover);
	--smart-text-normal: var(--text-normal);
	--smart-text-muted: var(--text-muted);
	--smart-text-faint: var(--text-faint);
	--smart-accent: var(--interactive-accent);
	
	/* 暗色模式下的Notion蓝色 */
	--notion-blue-bg: var(--background-modifier-hover);
	--notion-blue-fg: var(--text-accent);
}

/* 系统偏好暗色模式 */
@media (prefers-color-scheme: dark) {
	:root {
		--smart-bg-primary: var(--background-primary, #2b3441);
		--smart-bg-secondary: var(--background-secondary, #363d49);
		--smart-border: var(--background-modifier-border, rgba(255, 255, 255, 0.14));
		--smart-hover: var(--background-modifier-hover, #404651);
		--smart-text-normal: var(--text-normal, #dcddde);
		--smart-text-muted: var(--text-muted, rgba(255, 255, 255, 0.55));
		--smart-text-faint: var(--text-faint, rgba(255, 255, 255, 0.35));
		--smart-accent: var(--interactive-accent, #7c3aed);
		
		/* 暗色模式下的Notion蓝色 */
		--notion-blue-bg: var(--background-modifier-hover, #404651);
		--notion-blue-fg: var(--text-accent, #a78bfa);
	}
}

.smart-qa-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: var(--smart-bg-primary);
	font-family: var(--notion-font-body);
	color: var(--smart-text-normal);
	overflow: hidden;
	--smart-qa-font-size: 16px; /* 使用Notion标准正文字号 */
	font-size: var(--smart-qa-font-size);
}

.smart-qa-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--notion-space-3) var(--notion-space-4);
	border-bottom: none;
	flex-shrink: 0;
}

.smart-qa-title {
	margin: 0;
	font-size: 18px; /* 介于body和h3之间 */
	font-weight: 600;
	color: var(--smart-text-normal);
}

.smart-qa-header-buttons {
	display: flex;
	gap: var(--notion-space-2);
	align-items: center;
	height: 32px;
}

/* 通用图标按钮：无边框、无背景、线框图标（全局生效） */
.smart-qa-container .smart-qa-btn-icon,
.notion-style .smart-qa-btn-icon,
.smart-qa-container button.smart-qa-btn-icon,
.notion-style button.smart-qa-btn-icon {
	width: 28px !important;
	height: 28px !important;
	padding: 0 !important;
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important;
	background: none !important;
	background-color: transparent !important;
	border: none !important;
	border-radius: 0 !important;
	box-shadow: none !important;
	color: var(--smart-text-muted) !important;
	outline: none !important;
	cursor: pointer !important;
	-webkit-appearance: none !important;
	appearance: none !important;
}

.smart-qa-container .smart-qa-btn-icon:hover,
.notion-style .smart-qa-btn-icon:hover,
.smart-qa-container .smart-qa-btn-icon:focus,
.notion-style .smart-qa-btn-icon:focus,
.smart-qa-container .smart-qa-btn-icon:active,
.notion-style .smart-qa-btn-icon:active {
	background: none !important;
	background-color: transparent !important;
	color: var(--smart-text-normal) !important;
	box-shadow: none !important;
	outline: none !important;
}

.smart-qa-container .smart-qa-btn-icon svg,
.notion-style .smart-qa-btn-icon svg {
	width: 18px !important;
	height: 18px !important;
	stroke-width: 1.6 !important;
}

.smart-qa-btn {
	border: none;
	border-radius: var(--notion-radius-2);
	padding: var(--notion-space-2) var(--notion-space-3);
	font-size: var(--smart-qa-font-size);
	cursor: pointer;
	transition: all 0.2s ease;
	background: transparent;
	color: var(--smart-text-normal);
}

.smart-qa-btn:hover { 
	background-color: var(--smart-hover);
	transition: background-color 0.15s ease;
}

.smart-qa-btn-primary { color: var(--smart-text-normal); }

.smart-qa-btn-secondary {
	background: var(--background-secondary, #f5f5f5);
}

/* 通用图标按钮（保底）：保持透明，无边框 */
.smart-qa-btn-icon {
	width: 28px;
	height: 28px;
	padding: 0;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border: none;
	background: transparent;
	box-shadow: none;
	appearance: none;
}

.smart-qa-btn-icon:hover { 
	background: var(--smart-hover);
	border-radius: var(--notion-radius-1);
}

.smart-qa-btn-icon svg { width: 18px; height: 18px; stroke-width: 1.6; }

.smart-qa-file-selection {
	border: none; /* 去掉外边框顶部灰线 */
	border-radius: 8px;
	background: transparent;
	flex-shrink: 0; /* Prevent shrinking */
}

.smart-qa-section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--notion-space-3) var(--notion-space-4);
	cursor: pointer;
}

/* 左侧折叠胶囊按钮（放入 header 左侧） */
.smart-qa-header-left { display: flex; align-items: center; }
.smart-qa-collapsed-chip {
	display: inline-flex;
	align-items: center;
	height: 28px;
	padding: 0 var(--notion-space-3);
	/* 去除外框线，降低存在感 */
	border: none;
	background: var(--background-modifier-hover, #e5e5e5);
	color: var(--smart-text-muted);
	border-radius: var(--notion-radius-2);
	font-size: 12px;
	line-height: 1;
	cursor: pointer;
	outline: none;
	transition: all 0.2s ease;
}
/* 提升优先级，确保覆盖主题样式 */
.smart-qa-container .smart-qa-collapsed-chip,
.notion-style .smart-qa-collapsed-chip {
	border: none !important;
	background: var(--smart-hover) !important;
	color: var(--smart-text-muted) !important;
	box-shadow: none !important;
	cursor: pointer !important;
	outline: none !important;
}

.smart-qa-collapsed-chip:hover {
	background: var(--smart-hover);
	color: var(--smart-text-normal);
	filter: brightness(0.95);
}

.smart-qa-section-title {
	font-weight: 500;
	font-size: 13px;
}

.smart-qa-section-collapse {
	min-width: 32px !important;
	height: 32px !important;
	padding: 0 !important;
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important;
	border: none !important;
	border-radius: var(--notion-radius-2) !important;
	background: transparent !important;
	color: var(--smart-text-muted) !important;
	box-shadow: none !important;
	cursor: pointer !important;
	transition: background-color 0.15s ease, color 0.15s ease;
}

.smart-qa-section-collapse:hover,
.smart-qa-section-collapse:focus {
	background: var(--smart-hover) !important;
	color: var(--smart-text-normal) !important;
	outline: none !important;
}

.smart-qa-section-collapse svg {
	width: 16px !important;
	height: 16px !important;
	stroke-width: 1.6 !important;
}

.smart-qa-file-selection-content {
	padding: 0 var(--notion-space-4) var(--notion-space-3);
}

.smart-qa-mode-buttons {
	display: flex;
	gap: 6px;
	margin-bottom: var(--notion-space-3);
}

.smart-qa-mode-btn {
	display: inline-flex;
	align-items: center;
	padding: 6px 14px;
	border: none;
	border-radius: var(--notion-radius-2);
	background: var(--background-modifier-hover, #e5e5e5);
	color: var(--smart-text-muted);
	cursor: pointer;
	font-size: 13px;
	line-height: 1;
	transition: background-color 0.15s ease, color 0.15s ease, box-shadow 0.15s ease;
	box-shadow: none;
}

.smart-qa-mode-btn:hover,
.smart-qa-mode-btn:focus {
	background: var(--smart-hover);
	color: var(--text-normal, #000000);
	outline: none;
}

.smart-qa-mode-btn-active {
	background: var(--smart-bg-secondary);
	color: var(--smart-accent);
	box-shadow: none;
	border: 1px solid var(--smart-accent);
}

.smart-qa-selection-status {
	font-size: 12px;
	color: var(--smart-text-muted);
	padding: var(--notion-space-2) var(--notion-space-3);
	background: var(--background-primary, #ffffff);
	border-radius: 6px;
	border: 1px solid var(--smart-border);
}

.smart-qa-messages {
	flex: 1;
	overflow-y: auto;
	padding: 24px 20px;
	display: flex;
	flex-direction: column;
	gap: 14px;
	min-height: 0; /* Important for flex child with overflow */
}

.smart-qa-message {
	display: flex;
	gap: 12px;
	max-width: 100%;
	position: relative;
}

.smart-qa-message-system {
	justify-content: center;
}

.smart-qa-message-system .smart-qa-message-content {
	background: var(--smart-hover);
	color: var(--smart-text-muted);
	text-align: left;
	font-size: var(--notion-fs-small);
}

.smart-qa-message-user {
	justify-content: flex-end;
}

.smart-qa-message-user .smart-qa-message-content {
	background: var(--notion-blue-bg);
	color: var(--notion-blue-fg);
	border: 1px solid var(--smart-border);
}

.smart-qa-message-assistant .smart-qa-message-content {
	background: var(--smart-bg-primary);
	border: 1px solid var(--smart-border);
}

.smart-qa-message-content {
	position: relative;
	padding: var(--notion-space-3) var(--notion-space-4);
	border-radius: var(--notion-radius-2);
	max-width: 72ch;
	width: fit-content;
	word-wrap: break-word;
	line-height: var(--notion-lh-body);
	box-shadow: none; /* Notion强调弱阴影 */
	font-size: var(--smart-qa-font-size);
}

.smart-qa-message-content.smart-qa-message-content-loading {
	display: flex;
	align-items: center;
	gap: 6px;
	min-height: 36px;
}

.smart-qa-message-content.smart-qa-message-content-with-actions {
	padding-bottom: 44px;
}

.smart-qa-loading-dots {
	display: inline-flex;
	gap: 6px;
}

.smart-qa-loading-dots span {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: var(--text-muted, #666666);
	opacity: 0.5;
	animation: smart-qa-dot-bounce 1.2s infinite ease-in-out;
}

.smart-qa-loading-dots span:nth-child(2) { animation-delay: 0.2s; }
.smart-qa-loading-dots span:nth-child(3) { animation-delay: 0.4s; }

/* Markdown 内容优化 - Notion标题规范 */
.smart-qa-message-content h1 {
	font-size: var(--notion-fs-h1);
	line-height: var(--notion-lh-h1);
	font-weight: 600;
	margin: var(--notion-space-5) 0 var(--notion-space-4);
}
.smart-qa-message-content h2 {
	font-size: var(--notion-fs-h2);
	line-height: var(--notion-lh-h2);
	font-weight: 600;
	margin: var(--notion-space-4) 0 var(--notion-space-3);
}
.smart-qa-message-content h3 {
	font-size: var(--notion-fs-h3);
	line-height: var(--notion-lh-h3);
	font-weight: 600;
	margin: var(--notion-space-3) 0 var(--notion-space-2);
}
.smart-qa-message-content p { margin: 0 0 var(--notion-space-2); }
.smart-qa-message-content ul { margin: 0.4em 0 0.4em 1.2em; }
.smart-qa-message-content li { margin: 0.25em 0; }
.smart-qa-message-content code { 
	background: var(--smart-hover);
	padding: 2px 4px;
	border-radius: var(--notion-radius-1);
	font-family: var(--notion-font-mono);
	font-size: 0.95em;
}
.smart-qa-message-content pre {
	background: rgba(135, 131, 120, 0.15); /* Notion代码块背景 */
	padding: var(--notion-space-3);
	border-radius: var(--notion-radius-2);
	overflow: auto;
	font-family: var(--notion-font-mono);
	font-size: 14px;
	line-height: 1.55;
}

/* 调试消息样式 */
.smart-qa-debug-message {
	background-color: var(--smart-hover);
	border-left: 2px solid var(--notion-rule); /* Notion引用样式 */
	margin: var(--notion-space-2) 0;
	padding: var(--notion-space-2) var(--notion-space-3);
	border-radius: var(--notion-radius-1);
	font-size: var(--notion-fs-small);
	opacity: 0.8;
}

.smart-qa-debug-message .smart-qa-message-content {
	color: var(--smart-text-muted);
	font-family: var(--font-monospace, 'Monaco', 'Menlo', 'Ubuntu Mono', monospace);
	background: transparent;
	border: none;
	box-shadow: none;
	padding: 8px;
}

.smart-qa-message-actions {
	position: absolute;
	bottom: 8px;
	left: 12px;
	display: flex;
	gap: 12px;
	align-items: center;
	z-index: 1;
}

.smart-qa-btn-icon-action {
	width: 32px;
	height: 32px;
	padding: 0;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background: none;
	background-color: transparent;
	border: none;
	border-radius: 6px;
	color: var(--smart-text-muted);
	cursor: pointer;
	transition: all 0.2s ease;
	outline: none;
}

.smart-qa-btn-icon-action:hover {
	background-color: var(--smart-hover);
	color: var(--text-normal, #000000);
}

.smart-qa-btn-icon-action svg {
	width: 16px;
	height: 16px;
	stroke-width: 1.8;
}

@keyframes smart-qa-dot-bounce {
	0%, 80%, 100% { transform: translateY(0); opacity: 0.3; }
	40% { transform: translateY(-4px); opacity: 1; }
}

.smart-qa-status {
	padding: 4px 8px;
	background: transparent;
	border: none;
	border-radius: 0;
	font-size: 10px;
	line-height: 1.2;
	color: var(--text-faint, #999999);
	margin: 4px 12px 0 12px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	flex-shrink: 0; /* Prevent shrinking */
}

.smart-qa-input-container {
	padding: var(--notion-space-3) !important;
	border-top: none !important;
	background: var(--background-primary, #ffffff) !important;
	flex-shrink: 0; /* Prevent shrinking */
}

.smart-qa-input-wrapper {
	position: relative !important;
	display: block !important;
	border: 1px solid var(--background-modifier-border, #dddddd) !important;
	border-radius: var(--notion-radius-2) !important;
	padding: var(--notion-space-3) var(--notion-space-3) 52px !important;
	background: var(--background-primary, #ffffff) !important;
	min-height: 56px !important;
	transition: box-shadow .15s ease, border-color .15s ease, background-color .15s ease;
}
/* 聚焦点亮：方案 M */
.smart-qa-input-wrapper.is-focused {
	border-color: var(--interactive-accent, #007acc);
	/* 内阴影 1px + 轻微外投影 */
	box-shadow: 0 0 0 1px var(--interactive-accent, #007acc) inset, 0 8px 20px rgba(0,0,0,0.20);
	/* 背景轻微提亮（3–5%）：用半透明白覆盖实现，兼容性更好 */
	background-image: linear-gradient(0deg, rgba(255,255,255,0.04), rgba(255,255,255,0.04));
	background-blend-mode: lighten;
}

.smart-qa-input {
	flex: 1;
	width: 100% !important;
	box-sizing: border-box !important;
	border: none !important;
	box-shadow: none !important;
	outline: none !important;
	resize: none !important;
	background: transparent !important;
	color: var(--smart-text-normal) !important;
	font-size: var(--smart-qa-font-size) !important;
	line-height: 1.5 !important;
	min-width: 0; /* Allow proper flex shrinking */
	overflow-y: auto; /* 超过最大高度时出现滚动 */
}

.smart-qa-input:focus {
	border: none !important;
	box-shadow: none !important;
}

/* 底部内嵌工具条 */
.smart-qa-input-toolbar {
	position: absolute;
	left: 12px;
	right: 12px;
	bottom: 8px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 8px;
	padding: 0; /* 去掉整条底栏的背景与边框 */
	background: transparent;
	border: none;
	border-radius: 0;
	box-shadow: none;
	z-index: 2; /* 确保发送按钮不被遮挡 */
}
.smart-qa-input-toolbar-left { display: flex; align-items: center; gap: 8px; min-width: 0; }
.smart-qa-input-toolbar-right { display: flex; align-items: center; gap: 12px; }

/* 发送按钮：与模型选择器相同的轻量样式 */
.smart-qa-input-toolbar .smart-qa-send-btn {
	display: inline-flex !important;
	width: 30px !important;
	height: 30px !important;
	padding: 0 !important;
	align-items: center !important;
	justify-content: center !important;
	border: none !important;
	border-radius: var(--notion-radius-2) !important;
	background: var(--background-secondary, #f5f5f5) !important;
	color: var(--smart-text-muted) !important;
	box-shadow: none !important;
	opacity: 1 !important;
	visibility: visible !important;
}
.smart-qa-input-toolbar .smart-qa-send-btn:hover {
	background: var(--smart-hover) !important;
	color: var(--smart-text-normal) !important;
}

.smart-qa-input-toolbar .smart-qa-send-btn svg {
	display: block;
	margin: auto;
}

.smart-qa-model-selector {
	appearance: none;
	background: var(--background-secondary, #f5f5f5);
	border: none;
	border-radius: var(--notion-radius-2);
	color: var(--smart-text-muted);
	font-size: 12px;
	padding: 6px 10px;
	max-width: fit-content;
	min-width: auto;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	cursor: pointer;
	box-shadow: none;
}
.smart-qa-model-selector.smart-qa-model-placeholder {
	opacity: 0.9;
}

.smart-qa-model-selector:focus {
	outline: none;
	border-color: var(--interactive-accent, #007acc);
}

.smart-qa-token-count {
	font-size: 11px;
	color: var(--text-faint, #999999);
	min-width: 30px;
	text-align: right;
	display: none;
	opacity: 0;
	transition: opacity .12s ease;
}
/* 仅在面板底部区域 hover 时显示 */
.smart-qa-input-container:hover .smart-qa-token-count {
	display: inline-block;
	opacity: 1;
}
.smart-qa-input-toolbar .smart-qa-send-btn:disabled { opacity: 0.45; filter: grayscale(20%); }

.smart-qa-file-options {
	padding: 12px 0;
}

.smart-qa-folder-selector,
.smart-qa-tag-selector {
	margin-bottom: var(--notion-space-3);
}

.smart-qa-label {
	display: block;
	font-size: 12px;
	font-weight: 500;
	margin-bottom: 6px;
	color: var(--text-normal, #000000);
}

.smart-qa-select {
	width: 100%;
	padding: 6px 10px;
	border: 1px solid var(--smart-border);
	border-radius: 6px;
	background: var(--background-primary, #ffffff);
	color: var(--text-normal, #000000);
	font-size: 13px;
}

.smart-qa-tag-container {
	display: flex;
	flex-direction: column;
	gap: 6px;
	max-height: 200px;
	overflow-y: auto;
	padding: 6px;
	border: 1px solid var(--smart-border);
	border-radius: 6px;
	background: var(--background-primary, #ffffff);
}

.smart-qa-tag-input-wrapper {
	display: flex;
	margin-bottom: 8px;
}

.smart-qa-tag-input {
	flex: 1;
	padding: 6px 10px;
	border: 1px solid var(--smart-border);
	border-radius: 6px;
	background: var(--background-primary, #ffffff);
	color: var(--text-normal, #000000);
	font-size: 13px;
}

.smart-qa-selected-chips {
	display: flex;
	flex-wrap: wrap;
	gap: 6px;
	margin-bottom: 8px;
}

.smart-qa-chip {
	display: inline-flex;
	align-items: center;
	gap: 6px;
	padding: 2px 8px;
	border-radius: var(--notion-radius-2);
	/* 自适应文字：保持 inline-flex + 内容内边距即可 */
	/* 降低存在感：使用更轻的背景且无边框 */
	background: var(--background-modifier-hover, #e5e5e5);
	border: none;
	color: var(--text-normal, #000000);
	font-size: 12px;
}
.smart-qa-container .smart-qa-chip,
.notion-style .smart-qa-chip,
.smart-qa-modal-overlay .smart-qa-chip {
	background: var(--smart-hover) !important;
	border: none !important;
	box-shadow: none !important;
	color: var(--smart-text-normal) !important;
}

.smart-qa-chip button {
	background: none;
	border: none;
	cursor: pointer;
	color: var(--smart-text-muted);
	padding: 0 2px;
}

.smart-qa-tag-item {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px;
}

.smart-qa-tag-item input[type="checkbox"] {
	margin: 0;
}

.smart-qa-tag-item label {
	flex: 1;
	font-size: 12px;
	cursor: pointer;
	margin: 0;
}

.smart-qa-no-tags {
	padding: 12px;
	text-align: center;
	color: var(--smart-text-muted);
	font-size: 12px;
}

/* 模态框样式 */
.smart-qa-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 9999;
	display: none;
	align-items: center;
	justify-content: center;
}

.smart-qa-modal-content {
	position: relative;
	background: var(--background-primary, #ffffff);
	border-radius: 12px;
	max-width: 600px;
	width: 90%;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.smart-qa-modal-header {
	padding: 24px 24px 16px;
	border-bottom: 1px solid var(--background-modifier-border, #dddddd);
}

.smart-qa-modal-title {
	margin: 0;
	font-size: calc(var(--smart-qa-font-size) * 1.285714);
	font-weight: 600;
	color: var(--text-normal, #000000);
}

.smart-qa-modal-body {
	padding: 24px;
}

body.smart-qa-modal-open {
	overflow: hidden;
}

