import { Item<PERSON>iew, <PERSON><PERSON><PERSON><PERSON><PERSON>, Notice, <PERSON><PERSON><PERSON><PERSON><PERSON>, setIcon } from 'obsidian';
import SmartQAPlugin from '../main';
import { AIService, AIMessage, StreamResponse } from './api';
import { FileProcessor, ProcessedContent } from './file-processor';
import { ConversationManager, ConversationMessage } from './conversation-manager';
import { getModelInfo } from './settings';

export const VIEW_TYPE_SMART_QA = 'smart-qa-view';

export class SmartQAView extends ItemView {
	plugin: SmartQAPlugin;
	private buildMarker: string;
	private messagesContainer: HTMLElement;
	private inputContainer: HTMLElement;
	private fileSelectionContainer: HTMLElement;
	private statusContainer: HTMLElement;
	private aiService: AIService;
	private fileProcessor: FileProcessor;
	private conversationManager: ConversationManager;
	private conversationHistory: AIMessage[] = [];
	private currentConversationId: string | null = null;
	private isProcessing = false;
	private currentSelection: {
		mode: 'all' | 'folder' | 'tags';
		selection?: string | string[];
	} = { mode: 'all' };
	private processedContent: ProcessedContent | null = null;

	// 标签输入与已选展示
	private tagInputEl: HTMLInputElement | null = null;
	private selectedChipsEl: HTMLElement | null = null;
	private availableTags: string[] = [];
	private currentModelId: string = '';
	private collapsedChipEl: HTMLElement | null = null;
	private selectionContentEl: HTMLElement | null = null;
	private selectionHeaderEl: HTMLElement | null = null;
	private selectionCollapseBtn: HTMLElement | null = null;
	private modalOverlay: HTMLElement | null = null;
	private modalContent: HTMLElement | null = null;
	private fileSelectionOriginalParent: HTMLElement | null = null;
	private localStyleEl: HTMLStyleElement | null = null;
	private fileSelectionPlaceholder: Comment | null = null;
	private prevSelectionHeaderDisplay: string = '';
	private prevSelectionContentDisplay: string = '';
	private isSelectionCollapsed = false;

	// 统一的 Markdown 渲染助手，兼容不同 Obsidian 版本
	private async renderMarkdownTo(element: HTMLElement, markdown: string): Promise<void> {
		const sourcePath = this.app.workspace.getActiveFile()?.path ?? '';
		const anyRenderer = MarkdownRenderer as unknown as { render?: Function; renderMarkdown?: Function };
		if (typeof anyRenderer.renderMarkdown === 'function') {
			await (anyRenderer.renderMarkdown as any)(markdown, element, sourcePath, this);
			return;
		}
		if (typeof anyRenderer.render === 'function') {
			await (anyRenderer.render as any)(this.app, markdown, element, sourcePath, this);
		}
	}

constructor(leaf: WorkspaceLeaf, plugin: SmartQAPlugin) {
		super(leaf);
		this.plugin = plugin;
		// 每次加载视图生成唯一标记，用于判断代码是否为最新版本
		this.buildMarker = 'build-' + new Date().toISOString() + '-' + Math.random().toString(36).slice(2, 7);
		this.fileProcessor = new FileProcessor(this.app);
		this.conversationManager = new ConversationManager(this.app, this.plugin);
		this.initializeAIService();
		this.loadConversations();
	}

	private async loadConversations() {
		await this.conversationManager.loadConversations();
		this.startNewConversation();
	}

	private startNewConversation() {
		this.currentConversationId = this.conversationManager.createConversation();
		this.conversationHistory = [];
		// 初始化当前模型ID（若已有选择则保留；为空时尝试使用默认值）
		if (!this.currentModelId) {
			this.currentModelId = this.plugin.settings.defaultModel;
		}
	}

	private initializeAIService() {
		this.aiService = new AIService(
			this.plugin.settings.openRouterApiKey,
			this.plugin.settings.googleAiApiKey
		);
	}

	getViewType() {
		return VIEW_TYPE_SMART_QA;
	}

	getDisplayText() {
		return 'Smart QA';
	}

	getIcon() {
		return 'message-circle';
	}

	async onOpen() {
		const container = this.containerEl.children[1] as HTMLElement;
		container.empty();
		container.addClass('smart-qa-container');

		// 提前注入样式，避免中途异常导致样式缺失
		this.safeApplyStyles();

		try {
			this.createHeader(container);
			this.createFileSelection(container);
			this.createMessages(container);
			this.createInputArea(container);
			this.createStatusBar(container);
		} finally {
			// 兜底再注入一次，确保样式始终存在
			this.safeApplyStyles();
		}
	}

	async onClose() {
		// 清理资源
	}
	
	// 一键测试当前设置的 API 连接与默认模型
	private async testApiConnectivity() {
		try {
			// 1) 检查 API Key
			const hasOpenRouter = !!this.plugin.settings.openRouterApiKey;
			const hasGoogle = !!this.plugin.settings.googleAiApiKey;
			if (!hasOpenRouter && !hasGoogle) {
				new Notice('Please set an API key in Settings first.');
				return;
			}
			
			// 2) 选择模型：优先当前选择，其次默认模型
			const chosenModelId = this.currentModelId || this.plugin.settings.defaultModel;
			if (!chosenModelId) {
				new Notice('Please choose a model (or configure a default model in Settings).');
				return;
			}
			const modelInfo = getModelInfo(this.plugin.settings, chosenModelId);
			if (!modelInfo) {
				new Notice('Selected model not found. Please reconfigure in Settings.');
				return;
			}
			const fullModelId = `${modelInfo.provider}/${modelInfo.modelId}`;
			
			// 3) 调用一次最小对话
			this.updateStatus('Testing...', `Pinging ${fullModelId}`);
			const messages = [ { role: 'user', content: 'ping' } ] as AIMessage[];
			const resp = await this.aiService.chat(messages, fullModelId, false);
			
			if (resp.error) {
				this.addMessage({ type: 'system', content: `❌ Test failed: ${resp.error}`, timestamp: new Date() });
				new Notice('API Test failed. See panel message for details.');
			} else {
				const preview = resp.content?.slice(0, 100) || '(empty)';
				this.addMessage({ type: 'system', content: `✅ API Test OK (model: ${fullModelId})\nPreview: ${preview}`, timestamp: new Date() });
				new Notice('API Test succeeded!');
			}
		} catch (e) {
			this.addMessage({ type: 'system', content: `❌ Test error: ${e instanceof Error ? e.message : String(e)}`, timestamp: new Date() });
			new Notice('API Test error. Check console/log.');
		} finally {
			this.updateStatus('Ready', this.getStatusText());
		}
	}

	// 运行一组 UI 体检，定位“输入框高度”和“发送按钮”问题
	private async runUIHealthCheck() {
		const log = (...args: any[]) => console.log('[SmartQA UI Debug]', ...args);
		const report: any = { buildMarker: this.buildMarker };
		try {
			const container = this.inputContainer;
			const wrapper = container?.querySelector('.smart-qa-input-wrapper') as HTMLElement | null;
			const textarea = container?.querySelector('.smart-qa-input') as HTMLTextAreaElement | null;
			const toolbar = container?.querySelector('.smart-qa-input-toolbar') as HTMLElement | null;
			const toolbarLeft = container?.querySelector('.smart-qa-input-toolbar-left') as HTMLElement | null;
			const toolbarRight = container?.querySelector('.smart-qa-input-toolbar-right') as HTMLElement | null;
			const sendBtn = container?.querySelector('.smart-qa-send-btn') as HTMLButtonElement | null;
			const tokenCount = container?.querySelector('.smart-qa-token-count') as HTMLElement | null;
			const firstChip = document.querySelector('.smart-qa-chip') as HTMLElement | null;
			const inModal = document.querySelector('.smart-qa-modal-overlay .smart-qa-chip') as HTMLElement | null;

			report.elements = {
				container: !!container,
				wrapper: !!wrapper,
				textarea: !!textarea,
				toolbar: !!toolbar,
				toolbarLeft: !!toolbarLeft,
				toolbarRight: !!toolbarRight,
				sendBtn: !!sendBtn,
				tokenCount: !!tokenCount,
				toolbarLeftChildren: toolbarLeft?.children?.length ?? 0,
				toolbarRightChildren: toolbarRight?.children?.length ?? 0,
				firstChip: !!firstChip,
				chipInModal: !!inModal
			};

			// 如果缺少发送按钮或 token 计数，现场插入一个临时 TEST 按钮验证 DOM 是否可写
			if ((!sendBtn || !tokenCount) && toolbarRight) {
				const tempBtn = document.createElement('button');
				tempBtn.className = 'smart-qa-btn';
				tempBtn.textContent = 'TEST';
				tempBtn.style.padding = '4px 8px';
				tempBtn.style.border = '1px solid var(--background-modifier-border)';
				tempBtn.addEventListener('click', () => {
					alert('TEST button clicked: DOM injection works');
				});
				toolbarRight.appendChild(tempBtn);
				report.tempButtonInjected = true;
			} else {
				report.tempButtonInjected = false;
			}

			// 发送按钮状态
			if (sendBtn) {
				const cs = getComputedStyle(sendBtn);
				report.sendButton = {
					display: cs.display,
					visibility: cs.visibility,
					opacity: cs.opacity,
					width: cs.width,
					height: cs.height,
					zIndex: cs.zIndex,
					pointerEvents: cs.pointerEvents,
					isInLayout: sendBtn.offsetParent !== null,
					bounding: sendBtn.getBoundingClientRect()
				};
			} else {
				report.sendButton = { exists: false };
			}

			// 输入框高度测试：写入 5 行文本，看高度是否变化并限制为 3 行
			if (textarea) {
				const before = { styleHeight: textarea.style.height, scrollH: textarea.scrollHeight };
				const testLines = ['a','b','c','d','e'].join('\n');
				textarea.value = testLines;
				textarea.dispatchEvent(new Event('input', { bubbles: true }));
				await new Promise(r => requestAnimationFrame(() => r(null)));
				const afterGrow = { styleHeight: textarea.style.height, scrollH: textarea.scrollHeight };
				// 复原
				textarea.value = '';
				textarea.dispatchEvent(new Event('input', { bubbles: true }));
				await new Promise(r => requestAnimationFrame(() => r(null)));
				const afterReset = { styleHeight: textarea.style.height, scrollH: textarea.scrollHeight };
				report.inputExpansion = { before, afterGrow, afterReset };
			}

			// 回车发送测试：填入“test”模拟 Enter 并检查是否被清空
			if (textarea) {
				textarea.value = 'test';
				textarea.dispatchEvent(new Event('input', { bubbles: true }));
				const ev = new KeyboardEvent('keydown', { key: 'Enter', bubbles: true });
				textarea.dispatchEvent(ev);
				await new Promise(r => setTimeout(r, 50));
				report.enterSend = { cleared: textarea.value.length === 0 };
			}

			// 样式注入情况
			const styleEl = (this as any).localStyleEl as HTMLStyleElement | null;
			report.styles = {
				stylePresent: !!styleEl,
				length: styleEl?.textContent?.length || 0
			};

			// 芯片样式快照
			if (firstChip) {
				const cs = getComputedStyle(firstChip);
				report.chipStyle = {
					background: cs.backgroundColor,
					border: cs.border,
					color: cs.color,
					inModal: !!inModal
				};
			}

			// Tab按钮状态检查
			const modeButtons = document.querySelectorAll('.smart-qa-mode-btn');
			report.tabButtons = {
				count: modeButtons.length,
				buttons: Array.from(modeButtons).map((btn, index) => {
					const isActive = btn.classList.contains('smart-qa-mode-btn-active');
					const cs = getComputedStyle(btn);
					return {
						index,
						text: btn.textContent,
						isActive,
						className: btn.className,
						background: cs.backgroundColor,
						color: cs.color,
						borderRadius: cs.borderRadius
					};
				})
			};

			// 弹窗状态检查
			const modalOverlay = document.querySelector('.smart-qa-modal-overlay');
			if (modalOverlay) {
				const modalModeButtons = modalOverlay.querySelectorAll('.smart-qa-mode-btn');

				report.modal = {
					exists: true,
					modalTabButtons: Array.from(modalModeButtons).map((btn, index) => {
						const isActive = btn.classList.contains('smart-qa-mode-btn-active');
						return {
							index,
							text: btn.textContent,
							isActive,
							className: btn.className
						};
					})
				};
			} else {
				report.modal = { exists: false };
			}

			log('Report:', report);
			this.addMessage({ type: 'system', content: `UI Debug Report\n${JSON.stringify(report, null, 2)}`, timestamp: new Date() });
		} catch (e) {
			log('Error during UI debug', e);
			this.addMessage({ type: 'system', content: `UI Debug Error: ${e instanceof Error ? e.message : String(e)}`, timestamp: new Date() });
		}
	}

	private createHeader(container: HTMLElement) {
		const header = container.createEl('div', { cls: 'smart-qa-header' });
		// 左侧：折叠胶囊入口
		const leftGroup = header.createEl('div', { cls: 'smart-qa-header-left' });
		const collapsedChip = leftGroup.createEl('button', { cls: 'smart-qa-collapsed-chip' });
		collapsedChip.style.display = 'none';
		collapsedChip.addEventListener('click', () => {
			this.showFileSelectionModal();
		});
		this.collapsedChipEl = collapsedChip;

		const buttonGroup = header.createEl('div', { cls: 'smart-qa-header-buttons' });
		
		// 保存笔记按钮（线性图标）
		const saveNoteBtn = buttonGroup.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-btn-icon',
			title: 'Save conversation as note'
		});
		setIcon(saveNoteBtn, 'save');
		saveNoteBtn.addEventListener('click', () => this.saveConversationAsNote());

		// 新对话按钮（线性图标）
		const newChatBtn = buttonGroup.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-btn-icon',
			title: 'New conversation'
		});
		setIcon(newChatBtn, 'plus');
		newChatBtn.addEventListener('click', () => this.newConversation());

		// 设置按钮（线性图标）
		const settingsBtn = buttonGroup.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-btn-icon',
			title: 'Settings'
		});
		setIcon(settingsBtn, 'settings');
		settingsBtn.addEventListener('click', () => this.openSettings());
		
		// 测试 API 按钮（线性图标）
		const testApiBtn = buttonGroup.createEl('button', {
			cls: 'smart-qa-btn smart-qa-btn-icon',
			title: 'Test API'
		});
		setIcon(testApiBtn, 'activity');
		testApiBtn.addEventListener('click', () => this.testApiConnectivity());
		
		// UI 调试按钮：定位发送按钮/输入框问题
		const debugUiBtn = buttonGroup.createEl('button', {
			cls: 'smart-qa-btn smart-qa-btn-icon',
			title: 'Debug UI'
		});
		setIcon(debugUiBtn, 'bug');
		debugUiBtn.addEventListener('click', () => this.runUIHealthCheck());
	}

	private createFileSelection(container: HTMLElement) {
		this.fileSelectionContainer = container.createEl('div', { cls: 'smart-qa-file-selection' });
		
		const selectionHeader = this.fileSelectionContainer.createEl('div', { cls: 'smart-qa-section-header' });
		selectionHeader.createEl('span', { text: 'File Selection', cls: 'smart-qa-section-title' });
		const collapseBtn = selectionHeader.createEl('button', {
			cls: 'smart-qa-section-collapse smart-qa-btn-icon',
			attr: { 'aria-label': 'Collapse file selection' }
		});
		setIcon(collapseBtn, 'chevron-up');
		collapseBtn.addEventListener('click', (event) => {
			event.preventDefault();
			event.stopPropagation();
			this.toggleSelectionCollapse();
		});
		this.selectionCollapseBtn = collapseBtn;
		
		const selectionContent = this.fileSelectionContainer.createEl('div', { cls: 'smart-qa-file-selection-content' });
		this.selectionContentEl = selectionContent;
		this.selectionHeaderEl = selectionHeader;
		this.setSelectionCollapse(false);
		
		// 选择模式按钮组
		const modeButtons = selectionContent.createEl('div', { cls: 'smart-qa-mode-buttons' });
		
		const allFilesBtn = modeButtons.createEl('button', { 
			cls: 'smart-qa-mode-btn smart-qa-mode-btn-active',
			text: 'All Files'
		});
		
		const folderBtn = modeButtons.createEl('button', { 
			cls: 'smart-qa-mode-btn',
			text: 'Folder'
		});
		
		const tagBtn = modeButtons.createEl('button', { 
			cls: 'smart-qa-mode-btn',
			text: 'Tags'
		});

		// 文件选择区域（初始隐藏）
		const fileOptions = selectionContent.createEl('div', { cls: 'smart-qa-file-options' });
		fileOptions.style.display = 'none';
		
		// 文件夹选择器（隐藏）
		const folderSelector = fileOptions.createEl('div', { cls: 'smart-qa-folder-selector' });
		folderSelector.style.display = 'none';
		folderSelector.createEl('label', { text: 'Select Folder:', cls: 'smart-qa-label' });
		const folderSelect = folderSelector.createEl('select', { cls: 'smart-qa-select' });
		
		// 标签选择器（隐藏）
		const tagSelector = fileOptions.createEl('div', { cls: 'smart-qa-tag-selector' });
		tagSelector.style.display = 'none';
		tagSelector.createEl('label', { text: 'Select Tags:', cls: 'smart-qa-label' });

		// 输入选择区域
		const tagInputWrapper = tagSelector.createEl('div', { cls: 'smart-qa-tag-input-wrapper' });
		const tagInput = tagInputWrapper.createEl('input', { type: 'text', cls: 'smart-qa-tag-input', attr: { list: 'smart-qa-tag-datalist', placeholder: '输入以筛选或按 Enter 添加，例如 #project' } }) as HTMLInputElement;
		const tagDataList = tagInputWrapper.createEl('datalist', { attr: { id: 'smart-qa-tag-datalist' } });

		// 已选标签 chips
		const selectedChips = tagSelector.createEl('div', { cls: 'smart-qa-selected-chips' });

		// 排序选择器
		const sortRow = tagSelector.createEl('div', { cls: 'smart-qa-tag-sort-row' });
		sortRow.style.margin = '6px 0';
		sortRow.createEl('span', { text: 'Sort:', cls: 'smart-qa-label' });
		const sortSelect = sortRow.createEl('select', { cls: 'smart-qa-select' });
		sortSelect.createEl('option', { value: 'count-desc', text: 'Count (desc)' });
		sortSelect.createEl('option', { value: 'count-asc', text: 'Count (asc)' });
		sortSelect.createEl('option', { value: 'alpha-asc', text: 'A → Z' });
		sortSelect.createEl('option', { value: 'alpha-desc', text: 'Z → A' });

		// 标签复选列表
		const tagContainer = tagSelector.createEl('div', { cls: 'smart-qa-tag-container' });

		// 状态显示
		const selectionStatus = selectionContent.createEl('div', { cls: 'smart-qa-selection-status' });
		selectionStatus.setText('All files in vault will be included');

		// 初始化选择器内容
		this.populateSelectors(folderSelect, tagContainer, tagDataList as HTMLElement, selectedChips as HTMLElement, tagInput, sortSelect);
		
		[allFilesBtn, folderBtn, tagBtn].forEach(btn => {
			btn.addEventListener('click', () => {
				// 移除所有按钮的active类
				modeButtons.querySelectorAll('.smart-qa-mode-btn').forEach(b => {
					b.classList.remove('smart-qa-mode-btn-active');
				});
				// 给当前按钮添加active类
				btn.classList.add('smart-qa-mode-btn-active');

				// 隐藏所有选择器
				folderSelector.style.display = 'none';
				tagSelector.style.display = 'none';
				fileOptions.style.display = 'none';

				// 显示相应的选择器
				if (btn.textContent === 'Folder') {
					fileOptions.style.display = 'block';
					folderSelector.style.display = 'block';
				} else if (btn.textContent === 'Tags') {
					fileOptions.style.display = 'block';
					tagSelector.style.display = 'block';
				}

				this.updateFileSelection(btn.textContent || '');
			});
		});
		
		// 文件夹选择事件
		folderSelect.addEventListener('change', () => {
			const selectedFolder = folderSelect.value;
			console.log(`[View] Folder selected: "${selectedFolder}"`);
			this.currentSelection = { mode: 'folder', selection: selectedFolder };
			this.processedContent = null;
			console.log(`[View] Current selection updated:`, this.currentSelection);
			this.loadSelectedFiles().then(() => {
				this.updateFileSelectionStatus();
			});
			// 自动高亮"Folder"模式按钮，避免用户误以为仍是 All Files
			modeButtons.querySelectorAll('.smart-qa-mode-btn').forEach(b => {
				b.classList.remove('smart-qa-mode-btn-active');
			});
			folderBtn.classList.add('smart-qa-mode-btn-active');
		});
		
		// 排序选择事件
		sortSelect.addEventListener('change', () => {
			this.populateSelectors(folderSelect, tagContainer, tagDataList as HTMLElement, selectedChips as HTMLElement, tagInput, sortSelect);
		});
	}

	private createMessages(container: HTMLElement) {
		this.messagesContainer = container.createEl('div', { cls: 'smart-qa-messages' });
	}

	private createStatusBar(container: HTMLElement) {
		this.statusContainer = container.createEl('div', { cls: 'smart-qa-status' });
		this.updateStatus('Ready', this.getStatusText());
	}

	private createInputArea(container: HTMLElement) {
		this.inputContainer = container.createEl('div', { cls: 'smart-qa-input-container' });
		
		const inputWrapper = this.inputContainer.createEl('div', { cls: 'smart-qa-input-wrapper' });
		
		const textarea = inputWrapper.createEl('textarea', { 
			cls: 'smart-qa-input',
			placeholder: 'Ask a question about your knowledge base...'
		});
		textarea.rows = 1;

		// 底部内嵌工具条（无 Ask、模型靠左）
		const toolbar = inputWrapper.createEl('div', { cls: 'smart-qa-input-toolbar' });
		const toolbarLeft = toolbar.createEl('div', { cls: 'smart-qa-input-toolbar-left' });
		const toolbarRight = toolbar.createEl('div', { cls: 'smart-qa-input-toolbar-right' });
		
		// 模型选择器（靠左）
		const modelSelector = toolbarLeft.createEl('select', { 
			cls: 'smart-qa-model-selector',
			title: 'Select AI Model'
		});
		try {
			this.populateModelSelector(modelSelector);
		} catch (e) {
			console.error('[SmartQA] populateModelSelector failed:', e);
			// 即使模型下拉失败，也不要阻断后续 UI 元素创建
		}
		
		// Token 计数显示（靠右）
		const tokenCount = toolbarRight.createEl('span', { 
			cls: 'smart-qa-token-count',
			text: '0'
		});

		// 发送按钮（圆形 accent）
		const sendBtn = toolbarRight.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-send-btn',
			title: 'Send (Enter) · Shift+Enter for newline',
			attr: { 'aria-label': 'Send' }
		});
		// 使用内联 SVG，避免图标库 ID 不存在导致不可见
		sendBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m22 2-7 20-4-9-9-4z"/><path d="m22 2-10 10"/></svg>`;
		
		// 确保按钮可见性
		sendBtn.style.visibility = 'visible';
		sendBtn.style.display = 'inline-flex';

		// 自动调整文本框高度 + UI 状态切换
		const updateTypingState = () => {
			const hasText = textarea.value.trim().length > 0;
			if (hasText) {
				inputWrapper.addClass('has-text');
			} else {
				inputWrapper.removeClass('has-text');
			}
			sendBtn.toggleAttribute('disabled', !hasText);
		};
		textarea.addEventListener('input', () => {
			// 自适应高度：最多 3 行（稳健获取行高）
			const cs = getComputedStyle(textarea);
			let lh = parseFloat(cs.lineHeight);
			if (Number.isNaN(lh)) {
				const fs = parseFloat(cs.fontSize);
				lh = !Number.isNaN(fs) ? Math.round(fs * 1.5) : 20;
			}
			const maxH = Math.max(24, Math.round(lh * 3));
			textarea.style.height = 'auto';
			textarea.style.height = Math.min(textarea.scrollHeight, maxH) + 'px';
			this.updateTokenCount(textarea.value, tokenCount);
			updateTypingState();
		});
		textarea.addEventListener('focus', () => inputWrapper.addClass('is-focused'));
		textarea.addEventListener('blur', () => inputWrapper.removeClass('is-focused'));

		// 发送消息
		let isComposing = false;
		textarea.addEventListener('compositionstart', () => { 
			console.log('Composition started');
			isComposing = true; 
		});
		textarea.addEventListener('compositionend', () => { 
			console.log('Composition ended');
			isComposing = false; 
		});
		
		const sendMessage = () => {
			console.log('sendMessage called');
			const message = textarea.value.trim();
			console.log('Message to send:', message);
			
			if (message) {
				console.log('Sending message:', message);
				this.handleUserMessage(message);
				textarea.value = '';
				textarea.style.height = 'auto';
				this.updateTokenCount('', tokenCount);
				updateTypingState(); // 重置按钮状态
			} else {
				console.log('No message to send (empty)');
			}
		};

		// 模型选择器事件
		modelSelector.addEventListener('change', () => {
			this.currentModelId = modelSelector.value;
			this.updateStatus('Ready', this.getStatusText());
		});

		sendBtn.addEventListener('click', sendMessage);
		
		textarea.addEventListener('keydown', (e: KeyboardEvent) => {
			console.log('Key pressed:', e.key, 'isComposing:', isComposing, 'shiftKey:', e.shiftKey);
			
			// Enter 发送；中文输入法合成期间不触发
			if (e.key === 'Enter' && !isComposing) {
				if (e.shiftKey) {
					console.log('Shift+Enter detected, allowing newline');
					return; // 允许 Shift+Enter 换行
				}
				console.log('Enter key detected, sending message');
				e.preventDefault();
				sendMessage();
			}
		});
	}

	private applyNotionStyles() {
		// 添加 Notion 风格的 CSS 类到当前视图容器
		const container = this.containerEl.children[1] as HTMLElement;
		container?.addClass('notion-style');
		
		// 优先把样式插入到本视图容器内，避免与主题/其它插件冲突
		if (!this.localStyleEl) {
			this.localStyleEl = document.createElement('style');
			this.localStyleEl.setAttribute('data-owner', 'smart-qa');
			container?.appendChild(this.localStyleEl);
		}
		this.localStyleEl.textContent = this.getNotionCSS();
	}

	private safeApplyStyles() {
		try {
			this.applyNotionStyles();
		} catch (e) {
			console.error('applyNotionStyles failed:', e);
		}
		// 下一帧再尝试一次，处理极端时序问题
		requestAnimationFrame(() => {
			try { this.applyNotionStyles(); } catch {}
		});
	}

	private getNotionCSS(): string {
		const baseFont = this.plugin.settings?.uiFontSize ?? 16; // Notion默认16px
		const clampedFont = Math.min(Math.max(baseFont, 10), 28);
		return `
		/* Notion Design System Variables */
		:root {
			/* 基础色彩 (Light) - Notion规范 */
			--notion-fg-default: #37352F;
			--notion-fg-muted: rgba(55, 53, 47, 0.6);
			--notion-bg-default: #FFFFFF;
			--notion-border: rgba(55, 53, 47, 0.16);
			--notion-rule: rgba(55, 53, 47, 0.09);
			
			/* Notion强调色 */
			--notion-gray-bg: #F1F1EF;    --notion-gray-fg: #787774;
			--notion-blue-bg: #E7F3F8;    --notion-blue-fg: #0B6E99;
			
			/* 字体系统 */
			--notion-font-body: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
			--notion-font-mono: "IBM Plex Mono", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
			
			/* 字号与行高 */
			--notion-fs-body: 16px;     --notion-lh-body: 1.6;
			--notion-fs-h1: 30px;       --notion-lh-h1: 1.25;
			--notion-fs-h2: 24px;       --notion-lh-h2: 1.30;
			--notion-fs-h3: 20px;       --notion-lh-h3: 1.40;
			--notion-fs-small: 14px;
			
			/* 间距系统 (4px网格) */
			--notion-space-1: 4px;
			--notion-space-2: 8px;
			--notion-space-3: 12px;
			--notion-space-4: 16px;
			--notion-space-5: 20px;
			--notion-space-6: 24px;
			
			/* 圆角系统 */
			--notion-radius-1: 3px;
			--notion-radius-2: 6px;
			
			/* 亮色模式变量 */
			--smart-bg-primary: var(--notion-bg-default);
			--smart-bg-secondary: var(--notion-gray-bg);
			--smart-border: var(--notion-border);
			--smart-hover: var(--notion-gray-bg);
			--smart-text-normal: var(--notion-fg-default);
			--smart-text-muted: var(--notion-fg-muted);
			--smart-text-faint: rgba(55, 53, 47, 0.4);
			--smart-accent: var(--notion-blue-fg);
		}
		
		/* 暗色模式覆盖 - 统一使用 Obsidian 深蓝色主题 */
		.theme-dark,
		.theme-dark :root,
		.mod-linux.theme-dark,
		.mod-windows.theme-dark,
		.mod-macos.theme-dark,
		body.theme-dark {
			--smart-bg-primary: var(--background-primary);
			--smart-bg-secondary: var(--background-secondary);
			--smart-border: var(--background-modifier-border);
			--smart-hover: var(--background-modifier-hover);
			--smart-text-normal: var(--text-normal);
			--smart-text-muted: var(--text-muted);
			--smart-text-faint: var(--text-faint);
			--smart-accent: var(--interactive-accent);
			
			/* 暗色模式下的Notion蓝色 */
			--notion-blue-bg: var(--background-modifier-hover);
			--notion-blue-fg: var(--text-accent);
		}
		
		/* 系统偏好暗色模式 */
		@media (prefers-color-scheme: dark) {
			:root {
				--smart-bg-primary: var(--background-primary, #2b3441);
				--smart-bg-secondary: var(--background-secondary, #363d49);
				--smart-border: var(--background-modifier-border, rgba(255, 255, 255, 0.14));
				--smart-hover: var(--background-modifier-hover, #404651);
				--smart-text-normal: var(--text-normal, #dcddde);
				--smart-text-muted: var(--text-muted, rgba(255, 255, 255, 0.55));
				--smart-text-faint: var(--text-faint, rgba(255, 255, 255, 0.35));
				--smart-accent: var(--interactive-accent, #7c3aed);
				
				/* 暗色模式下的Notion蓝色 */
				--notion-blue-bg: var(--background-modifier-hover, #404651);
				--notion-blue-fg: var(--text-accent, #a78bfa);
			}
		}
		
		.smart-qa-container {
			display: flex;
			flex-direction: column;
			height: 100%;
			background: var(--smart-bg-primary);
			font-family: var(--notion-font-body);
			color: var(--smart-text-normal);
			overflow: hidden;
			--smart-qa-font-size: ${clampedFont}px;
			font-size: var(--smart-qa-font-size);
		}

		.smart-qa-header {
			display: flex;
			justify-content: space-between; /* 左侧胶囊 + 右侧图标 */
			align-items: center;
			padding: var(--notion-space-3) var(--notion-space-4);
			border-bottom: none;
			flex-shrink: 0; /* Prevent shrinking */
		}

			.smart-qa-title {
				margin: 0;
				font-size: 18px;
				font-weight: 600;
				color: var(--smart-text-normal);
			}

		.smart-qa-header-buttons {
			display: flex;
			gap: var(--notion-space-2);
			align-items: center;
			height: 32px; /* 与左侧胶囊高度一致，视觉对齐 */
		}

		/* 通用图标按钮：无边框、无背景、线框图标（全局生效） */
		.smart-qa-container .smart-qa-btn-icon,
		.notion-style .smart-qa-btn-icon,
		.smart-qa-container button.smart-qa-btn-icon,
		.notion-style button.smart-qa-btn-icon {
			width: 28px !important;
			height: 28px !important;
			padding: 0 !important;
			display: inline-flex !important;
			align-items: center !important;
			justify-content: center !important;
			background: none !important;
			background-color: transparent !important;
			border: none !important;
			border-radius: 0 !important;
			box-shadow: none !important;
			color: var(--smart-text-muted) !important;
			outline: none !important;
			cursor: pointer !important;
			-webkit-appearance: none !important;
			appearance: none !important;
		}
		
		.smart-qa-container .smart-qa-btn-icon:hover,
		.notion-style .smart-qa-btn-icon:hover,
		.smart-qa-container .smart-qa-btn-icon:focus,
		.notion-style .smart-qa-btn-icon:focus,
		.smart-qa-container .smart-qa-btn-icon:active,
		.notion-style .smart-qa-btn-icon:active {
			background: none !important;
			background-color: transparent !important;
			color: var(--smart-text-normal) !important;
			box-shadow: none !important;
			outline: none !important;
		}
		
		.smart-qa-container .smart-qa-btn-icon svg,
		.notion-style .smart-qa-btn-icon svg {
			width: 18px !important;
			height: 18px !important;
			stroke-width: 1.6 !important;
		}

			.smart-qa-btn {
				border: none;
				border-radius: var(--notion-radius-2);
				padding: var(--notion-space-2) var(--notion-space-3);
				font-size: var(--smart-qa-font-size);
				cursor: pointer;
			transition: all 0.2s ease;
			background: transparent;
			color: var(--smart-text-normal);
		}

		.smart-qa-btn:hover { 
			background-color: var(--notion-gray-bg, var(--background-modifier-hover));
			transition: background-color 0.15s ease;
		}

		.smart-qa-btn-primary { color: var(--smart-text-normal); }

		.smart-qa-btn-secondary {
			background: var(--background-secondary);
		}

		/* 通用图标按钮（保底）：保持透明，无边框 */
		.smart-qa-btn-icon {
			width: 28px;
			height: 28px;
			padding: 0;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			border: none;
			background: transparent;
			box-shadow: none;
			appearance: none;
		}

		.smart-qa-btn-icon:hover { background: transparent; }

		.smart-qa-btn-icon svg { width: 18px; height: 18px; stroke-width: 1.6; }

		.smart-qa-file-selection {
			border: none; /* 去掉外边框顶部灰线 */
			border-radius: 8px;
			background: transparent;
			flex-shrink: 0; /* Prevent shrinking */
		}

		.smart-qa-section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: var(--notion-space-3) var(--notion-space-4);
			cursor: pointer;
		}

		/* 左侧折叠胶囊按钮（放入 header 左侧） */
		.smart-qa-header-left { display: flex; align-items: center; }
			.smart-qa-collapsed-chip {
				display: inline-flex;
				align-items: center;
				height: 28px;
				padding: 0 10px;
				/* 去除外框线，降低存在感 */
				border: none;
				background: var(--smart-hover);
				color: var(--text-muted);
				border-radius: var(--notion-radius-2);
				font-size: 12px;
			line-height: 1;
		}
		/* 提升优先级，确保覆盖主题样式 */
		.smart-qa-container .smart-qa-collapsed-chip,
		.notion-style .smart-qa-collapsed-chip {
			border: none !important;
			background: var(--background-modifier-hover) !important;
			color: var(--smart-text-muted) !important;
			box-shadow: none !important;
		}

		.smart-qa-section-title {
			font-weight: 500;
			font-size: 13px;
		}

		.smart-qa-section-collapse {
			min-width: 32px !important;
			height: 32px !important;
			padding: 0 !important;
			display: inline-flex !important;
			align-items: center !important;
			justify-content: center !important;
			border: none !important;
			border-radius: 10px !important;
			background: transparent !important;
			color: var(--smart-text-muted) !important;
			box-shadow: none !important;
			cursor: pointer !important;
			transition: background-color 0.15s ease, color 0.15s ease;
		}

		.smart-qa-section-collapse:hover,
		.smart-qa-section-collapse:focus {
			background: var(--background-modifier-hover) !important;
			color: var(--smart-text-normal) !important;
			outline: none !important;
		}

		.smart-qa-section-collapse svg {
			width: 16px !important;
			height: 16px !important;
			stroke-width: 1.6 !important;
		}


		.smart-qa-file-selection-content {
			padding: 0 16px 12px;
		}

			.smart-qa-mode-buttons {
				display: flex;
				gap: 6px;
				margin-bottom: var(--notion-space-3);
			}

			.smart-qa-mode-btn {
				display: inline-flex;
				align-items: center;
				padding: 6px 14px;
				border: none;
				border-radius: var(--notion-radius-2);
				background: var(--smart-hover);
				color: var(--text-muted);
				cursor: pointer;
				font-size: 12px;
				line-height: 1;
				transition: background-color 0.15s ease, color 0.15s ease, box-shadow 0.15s ease;
				box-shadow: none;
			}

			.smart-qa-mode-btn:hover,
			.smart-qa-mode-btn:focus {
				background: var(--background-modifier-border);
				color: var(--text-normal);
				outline: none;
			}

			.smart-qa-mode-btn-active {
				background: var(--interactive-accent);
				color: var(--text-on-accent);
				box-shadow: none;
				border: 1px solid var(--notion-blue-fg, var(--interactive-accent));
			}

			.smart-qa-selection-status {
				font-size: 12px;
				color: var(--text-muted);
			padding: 8px 12px;
			background: var(--background-primary);
			border-radius: 6px;
			border: 1px solid var(--background-modifier-border);
		}

		.smart-qa-messages {
			flex: 1;
			overflow-y: auto;
			padding: 24px 20px;
			display: flex;
			flex-direction: column;
			gap: 14px;
			min-height: 0; /* Important for flex child with overflow */
		}

		.smart-qa-message {
			display: flex;
			gap: 12px;
			max-width: 100%;
		}

		.smart-qa-message-system {
			justify-content: center;
		}

			.smart-qa-message-system .smart-qa-message-content {
				background: var(--notion-gray-bg, var(--background-secondary));
				color: var(--smart-text-muted);
				text-align: left;
				font-size: var(--notion-fs-small);
			}

		.smart-qa-message-user {
			justify-content: flex-end;
		}

		.smart-qa-message-user .smart-qa-message-content {
			background: var(--notion-blue-bg);
			color: var(--notion-blue-fg);
			border: 1px solid var(--smart-border);
		}

		.smart-qa-message-assistant .smart-qa-message-content {
			background: var(--smart-bg-primary);
			border: 1px solid var(--smart-border);
		}

			.smart-qa-message-content {
				position: relative;
				padding: var(--notion-space-3) var(--notion-space-4);
				border-radius: var(--notion-radius-2);
				max-width: 72ch;
				width: fit-content;
				word-wrap: break-word;
				line-height: var(--notion-lh-body);
				box-shadow: none; /* Notion强调弱阴影 */
				font-size: var(--smart-qa-font-size);
			}
			.smart-qa-message-content.smart-qa-message-content-with-actions {
				padding-bottom: 44px;
			}
			.smart-qa-message-content-loading {
				display: flex;
				align-items: center;
				gap: 6px;
				min-height: 36px;
			}
			.smart-qa-loading-dots {
				display: inline-flex;
				gap: 6px;
			}
			.smart-qa-loading-dots span {
				width: 6px;
				height: 6px;
				border-radius: 50%;
				background: var(--text-muted);
				opacity: 0.5;
				animation: smart-qa-dot-bounce 1.2s infinite ease-in-out;
			}
			.smart-qa-loading-dots span:nth-child(2) { animation-delay: 0.2s; }
			.smart-qa-loading-dots span:nth-child(3) { animation-delay: 0.4s; }

		/* Markdown 内容优化 */
		/* Markdown 内容优化 - Notion标题规范 */
		.smart-qa-message-content h1 {
			font-size: var(--notion-fs-h1);
			line-height: var(--notion-lh-h1);
			font-weight: 600;
			margin: var(--notion-space-5) 0 var(--notion-space-4);
		}
		.smart-qa-message-content h2 {
			font-size: var(--notion-fs-h2);
			line-height: var(--notion-lh-h2);
			font-weight: 600;
			margin: var(--notion-space-4) 0 var(--notion-space-3);
		}
		.smart-qa-message-content h3 {
			font-size: var(--notion-fs-h3);
			line-height: var(--notion-lh-h3);
			font-weight: 600;
			margin: var(--notion-space-3) 0 var(--notion-space-2);
		}
		.smart-qa-message-content p { margin: 0 0 var(--notion-space-2); }
		.smart-qa-message-content ul { margin: 0 0 var(--notion-space-2) 24px; }
		.smart-qa-message-content li { margin: 0 0 6px; }
		.smart-qa-message-content code { 
			background: var(--smart-hover);
			padding: 2px 4px;
			border-radius: var(--notion-radius-1);
			font-family: var(--notion-font-mono);
			font-size: 0.95em;
		}
		.smart-qa-message-content pre {
			background: rgba(135, 131, 120, 0.15); /* Notion代码块背景 */
			padding: var(--notion-space-3);
			border-radius: var(--notion-radius-2);
			overflow: auto;
			font-family: var(--notion-font-mono);
			font-size: 14px;
			line-height: 1.55;
		}

			.smart-qa-message-actions {
				position: absolute;
				bottom: 8px;
				left: 12px;
			display: flex;
			gap: 12px;
			align-items: center;
			z-index: 1;
		}

		.smart-qa-btn-icon-action {
			width: 32px;
			height: 32px;
			padding: 0;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			background: none;
			background-color: transparent;
			border: none;
			border-radius: 6px;
			color: var(--text-muted);
			cursor: pointer;
			transition: all 0.2s ease;
			outline: none;
		}

		.smart-qa-btn-icon-action:hover {
			background-color: var(--background-modifier-hover);
			color: var(--text-normal);
		}

			.smart-qa-btn-icon-action svg {
				width: 16px;
				height: 16px;
				stroke-width: 1.8;
			}
			@keyframes smart-qa-dot-bounce {
				0%, 80%, 100% { transform: translateY(0); opacity: 0.3; }
				40% { transform: translateY(-4px); opacity: 1; }
			}

			.smart-qa-status {
				padding: 4px 8px;
				background: transparent;
				border: none;
				border-radius: 0;
				font-size: 10px;
			line-height: 1.2;
			color: var(--text-faint);
			margin: 4px 12px 0 12px;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			flex-shrink: 0; /* Prevent shrinking */
		}

		.smart-qa-input-container {
			padding: 10px 12px !important;
			border-top: none !important;
			background: var(--background-primary) !important;
			flex-shrink: 0; /* Prevent shrinking */
		}

		.smart-qa-input-wrapper {
			position: relative !important;
			display: block !important;
			border: 1px solid var(--background-modifier-border) !important;
			border-radius: 12px !important;
			padding: var(--notion-space-3) var(--notion-space-3) 52px !important;
			background: var(--background-primary) !important;
			min-height: 56px !important;
			transition: box-shadow .15s ease, border-color .15s ease, background-color .15s ease;
		}
		/* 聚焦点亮：方案 M */
		.smart-qa-input-wrapper.is-focused {
			border-color: var(--interactive-accent);
			/* 内阴影 1px + 轻微外投影 */
			box-shadow: 0 0 0 1px var(--interactive-accent) inset, 0 8px 20px rgba(0,0,0,0.20);
			/* 背景轻微提亮（3–5%）：用半透明白覆盖实现，兼容性更好 */
			background-image: linear-gradient(0deg, rgba(255,255,255,0.04), rgba(255,255,255,0.04));
			background-blend-mode: lighten;
		}

			.smart-qa-input {
				flex: 1;
				width: 100% !important;
				box-sizing: border-box !important;
				border: none !important;
				box-shadow: none !important;
				outline: none !important;
				resize: none !important;
				background: transparent !important;
				color: var(--smart-text-normal) !important;
					font-size: var(--smart-qa-font-size) !important;
				line-height: 1.5 !important;
				min-width: 0; /* Allow proper flex shrinking */
				overflow-y: auto; /* 超过最大高度时出现滚动 */
			}
			.smart-qa-input:focus {
				border: none !important;
				box-shadow: none !important;
			}

		/* 底部内嵌工具条 */
		.smart-qa-input-toolbar {
			position: absolute;
			left: 12px;
			right: 12px;
			bottom: 8px;
			height: 36px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: var(--notion-space-2);
			padding: 0; /* 去掉整条底栏的背景与边框 */
			background: transparent;
			border: none;
			border-radius: 0;
			box-shadow: none;
			z-index: 2; /* 确保发送按钮不被遮挡 */
		}
		.smart-qa-input-toolbar-left { display: flex; align-items: center; gap: 8px; min-width: 0; }
		.smart-qa-input-toolbar-right { display: flex; align-items: center; gap: 12px; }

		/* 发送按钮：与模型选择器相同的轻量样式 */
		.smart-qa-input-toolbar .smart-qa-send-btn {
			display: inline-flex !important;
			width: 30px !important;
			height: 30px !important;
			padding: 0 !important;
			align-items: center !important;
			justify-content: center !important;
			border: none !important;
			border-radius: 50% !important;
			background: var(--background-secondary) !important;
			color: var(--smart-text-muted) !important;
			box-shadow: none !important;
			opacity: 1 !important;
			visibility: visible !important;
		}
		.smart-qa-input-toolbar .smart-qa-send-btn:hover {
			background: var(--background-modifier-hover) !important;
			color: var(--smart-text-normal) !important;
		}

		.smart-qa-input-toolbar .smart-qa-send-btn svg {
			display: block;
			margin: auto;
		}

			.smart-qa-model-selector {
				appearance: none;
				background: var(--background-secondary);
				border: none;
				border-radius: var(--notion-radius-2);
				color: var(--text-muted);
				font-size: 12px;
				padding: 6px 10px;
			max-width: fit-content;
			min-width: auto;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			cursor: pointer;
			box-shadow: none;
		}
		.smart-qa-model-selector.smart-qa-model-placeholder {
			opacity: 0.9;
		}

		.smart-qa-model-selector:focus {
			outline: none;
			border-color: var(--interactive-accent);
		}

			.smart-qa-token-count {
				font-size: 11px;
				color: var(--text-faint);
			min-width: 30px;
			text-align: right;
			display: none;
			opacity: 0;
			transition: opacity .12s ease;
		}
		/* 仅在面板底部区域 hover 时显示 */
		.smart-qa-input-container:hover .smart-qa-token-count {
			display: inline-block;
			opacity: 1;
		}
		.smart-qa-input-toolbar .smart-qa-send-btn:disabled { opacity: 0.45; filter: grayscale(20%); }

		.smart-qa-file-options {
			padding: 12px 0;
		}

		.smart-qa-folder-selector,
		.smart-qa-tag-selector {
			margin-bottom: 12px;
		}

			.smart-qa-label {
				display: block;
				font-size: 12px;
				font-weight: 500;
				margin-bottom: 6px;
				color: var(--text-normal);
			}

			.smart-qa-select {
				width: 100%;
				padding: 6px 10px;
				border: 1px solid var(--background-modifier-border);
				border-radius: 6px;
				background: var(--background-primary);
				color: var(--text-normal);
				font-size: 13px;
			}

		.smart-qa-tag-container {
			display: flex;
			flex-direction: column;
			gap: 6px;
			max-height: 200px;
			overflow-y: auto;
			padding: 6px;
			border: 1px solid var(--background-modifier-border);
			border-radius: 6px;
			background: var(--background-primary);
		}

		.smart-qa-tag-input-wrapper {
			display: flex;
			margin-bottom: 8px;
		}

			.smart-qa-tag-input {
				flex: 1;
				padding: 6px 10px;
				border: 1px solid var(--background-modifier-border);
				border-radius: 6px;
				background: var(--background-primary);
				color: var(--text-normal);
				font-size: 13px;
			}

		.smart-qa-selected-chips {
			display: flex;
			flex-wrap: wrap;
			gap: 6px;
			margin-bottom: 8px;
		}

			.smart-qa-chip {
				display: inline-flex;
				align-items: center;
				gap: 6px;
				padding: 2px 8px;
				border-radius: var(--notion-radius-2);
				/* 自适应文字：保持 inline-flex + 内容内边距即可 */
				/* 降低存在感：使用更轻的背景且无边框 */
				background: var(--smart-hover);
				border: none;
				color: var(--text-normal);
				font-size: 12px;
			}
		.smart-qa-container .smart-qa-chip,
		.notion-style .smart-qa-chip,
		.smart-qa-modal-overlay .smart-qa-chip {
			background: var(--background-modifier-hover) !important;
			border: none !important;
			box-shadow: none !important;
			color: var(--smart-text-normal) !important;
		}

		.smart-qa-chip button {
			background: none;
			border: none;
			cursor: pointer;
			color: var(--text-muted);
			padding: 0 2px;
		}

		.smart-qa-tag-item {
			display: flex;
			align-items: center;
			gap: var(--notion-space-2);
			padding: 4px;
		}

		.smart-qa-tag-item input[type="checkbox"] {
			margin: 0;
		}

			.smart-qa-tag-item label {
				flex: 1;
				font-size: 12px;
				cursor: pointer;
				margin: 0;
			}

			.smart-qa-no-tags {
				padding: var(--notion-space-3);
				text-align: center;
				color: var(--text-muted);
				font-size: 12px;
			}

		/* 模态框样式 */
		.smart-qa-modal-overlay {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.7);
			z-index: 9999;
			display: none;
			align-items: center;
			justify-content: center;
		}

		.smart-qa-modal-content {
			position: relative;
			background: var(--background-primary);
			border-radius: 12px;
			max-width: 600px;
			width: 90%;
			max-height: 80vh;
			overflow-y: auto;
			box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
		}

		body.smart-qa-modal-open {
			overflow: hidden;
		}
		`;
	}

	private newConversation() {
		this.startNewConversation();
		this.messagesContainer.empty();
		this.addMessage({
			type: 'system',
			content: 'Started new conversation. Select your files and ask questions about your knowledge base.',
			timestamp: new Date()
		});
	}

	public updateAIService() {
		this.initializeAIService();
		// 重新填充模型选择器
		const modelSelector = this.inputContainer?.querySelector('.smart-qa-model-selector') as HTMLSelectElement;
		if (modelSelector) {
			this.populateModelSelector(modelSelector);
		}
		// 应用可能更新的样式设置（如字体大小）
		this.safeApplyStyles();
	}

	private populateModelSelector(selector: HTMLSelectElement) {
		selector.empty();
		
		if (this.plugin.settings.customModels.length === 0) {
			selector.createEl('option', { value: '', text: 'Select a model…' });
			selector.addClass('smart-qa-model-placeholder');
			selector.title = 'Open settings to add models';
			// 点击时打开设置
			const open = () => this.openSettings();
			selector.addEventListener('mousedown', open, { once: false });
			selector.addEventListener('keydown', (e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); open(); } });
			this.currentModelId = '';
			this.updateStatus('Ready', this.getStatusText());
			return;
		}
		
		// 填充模型选项，并尽量保持名称在 28ch 内显示
		this.plugin.settings.customModels.forEach(model => {
			const name = model.name;
			selector.createEl('option', { 
				value: model.id, 
				text: name, 
				attr: { title: name }
			});
		});
		selector.removeClass('smart-qa-model-placeholder');
		
		// 设置选择：优先默认模型；无默认或默认无效则选第一个
		const defaultId = this.plugin.settings.defaultModel;
		const hasDefault = !!defaultId && this.plugin.settings.customModels.some(m => m.id === defaultId);
		const selectedId = hasDefault ? (defaultId as string) : this.plugin.settings.customModels[0].id;
		selector.value = selectedId;
		this.currentModelId = selectedId;
		// 刷新状态栏以反映当前模型
		this.updateStatus('Ready', this.getStatusText());
	}

	private getStatusText(): string {
		let statusText = '';
		
		// 显示当前模型
		if (this.currentModelId) {
			const modelInfo = getModelInfo(this.plugin.settings, this.currentModelId);
			statusText += `Model: ${modelInfo?.name || 'Unknown'}`;
		} else {
			statusText += 'Model: None selected';
		}
		
		// 显示文件信息
		if (this.processedContent) {
			statusText += ` | ${this.processedContent.summary}`;
		} else {
			statusText += ' | Files: All vault';
		}
		
		return statusText;
	}

	private async saveConversationAsNote() {
		if (!this.currentConversationId) {
			new Notice('No conversation to save');
			return;
		}

		try {
			const file = await this.conversationManager.saveAsNote(this.currentConversationId);
			
			// 询问用户是否要打开笔记
			setTimeout(() => {
				const openNote = confirm('Conversation saved! Open the note?');
				if (openNote) {
					this.app.workspace.getLeaf().openFile(file);
				}
			}, 100);
		} catch (error) {
			new Notice(`Failed to save conversation: ${error.message}`);
		}
	}

	private async loadSelectedFiles() {
		try {
			console.log(`[View] Loading files with selection:`, this.currentSelection);
			// 获取选定的文件
			const files = await this.fileProcessor.getFilesBySelection(
				this.currentSelection.mode,
				this.currentSelection.selection
			);
			console.log(`[View] Found ${files.length} files`);

			if (files.length === 0) {
				console.log(`[View] No files found for selection`);
				this.processedContent = {
					files: [],
					totalWordCount: 0,
					summary: 'No files selected'
				};
				return;
			}

			// 获取完整的文件统计信息（用于显示）
			console.log(`[View] Getting file stats for ${files.length} files...`);
			const originalStats = await this.fileProcessor.getFileStats(files);
			console.log(`[View] Original stats:`, originalStats);

			// 处理文件内容
			console.log(`[View] Processing files...`);
			const processed = await this.fileProcessor.processFiles(files);
			console.log(`[View] Processed content:`, processed);

			// 检查 token 限制并截断内容，但保留原始统计信息用于显示
			const maxTokens = Math.floor(this.plugin.settings.maxContextTokens * 0.8); // 保留20%给对话
			this.processedContent = this.fileProcessor.truncateContent(processed, maxTokens, originalStats);
			console.log(`[View] Final processed content:`, this.processedContent);

			// 更新状态显示
			this.updateFileSelectionStatus();
			
		} catch (error) {
			console.error('Failed to load selected files:', error);
			this.processedContent = {
				files: [],
				totalWordCount: 0,
				summary: 'Error loading files'
			};
		}
	}

	private updateFileSelectionStatus() {
		if (!this.processedContent) return;

		const statusElement = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
		if (statusElement) {
			statusElement.textContent = this.processedContent.summary;
		}
		this.updateCollapsedChipLabel();
	}

	private updateCollapsedChipLabel() {
		if (!this.collapsedChipEl) return;
		const mode = this.currentSelection.mode;
		let label = 'vault';
		if (mode === 'folder' && typeof this.currentSelection.selection === 'string' && this.currentSelection.selection) {
			label = this.currentSelection.selection.split('/').pop() || 'folder';
		} else if (mode === 'tags' && Array.isArray(this.currentSelection.selection)) {
			label = `tags(${this.currentSelection.selection.length})`;
		}
		this.collapsedChipEl.textContent = label + ' ▼';
	}

	private showFileSelectionModal() {
		this.setSelectionCollapse(false);
		// 首次创建蒙层与内容容器
		if (!this.modalOverlay) {
			this.modalOverlay = document.body.createEl('div', { cls: 'smart-qa-modal-overlay' });
			this.modalContent = this.modalOverlay.createEl('div', { cls: 'smart-qa-modal-content' });
			// 点击蒙层关闭
			this.modalOverlay.addEventListener('click', (e) => {
				if (e.target === this.modalOverlay) this.hideFileSelectionModal();
			});
		}
		
		// 将真实的文件选择区域移动到模态框中（不再克隆，保留所有事件）
		if (this.fileSelectionContainer && this.modalContent) {
			// 如果之前已经在模态框中，就不重复处理
			if (this.fileSelectionContainer.parentElement !== this.modalContent) {
				// 清理 modalContent，避免累积残留节点
				this.modalContent.empty();
				// 记录原位置并放置占位符
				this.fileSelectionOriginalParent = this.fileSelectionContainer.parentElement as HTMLElement;
				this.fileSelectionPlaceholder = document.createComment('smart-qa-return-placeholder');
				this.fileSelectionOriginalParent?.insertBefore(this.fileSelectionPlaceholder, this.fileSelectionContainer);
				// 移动进模态框
				this.modalContent.appendChild(this.fileSelectionContainer);
				// 记住并强制展开内容
				this.prevSelectionHeaderDisplay = this.selectionHeaderEl?.style.display || '';
				this.prevSelectionContentDisplay = this.selectionContentEl?.style.display || '';
				if (this.selectionHeaderEl) this.selectionHeaderEl.style.display = 'flex';
				if (this.selectionContentEl) this.selectionContentEl.style.display = 'block';
			}
		}
		
		// 显示模态框
		if (this.modalOverlay) {
			this.modalOverlay.style.display = 'flex';
			document.body.addClass('smart-qa-modal-open');
		}
	}

	private hideFileSelectionModal() {
		// 隐藏模态框
		if (this.modalOverlay) {
			this.modalOverlay.style.display = 'none';
			document.body.removeClass('smart-qa-modal-open');
		}
		// 将文件选择区域放回原位置并还原显示状态
		if (this.fileSelectionOriginalParent && this.fileSelectionPlaceholder && this.fileSelectionContainer) {
			this.fileSelectionOriginalParent.insertBefore(this.fileSelectionContainer, this.fileSelectionPlaceholder);
			this.fileSelectionPlaceholder.remove();
			this.fileSelectionPlaceholder = null;
			if (this.selectionHeaderEl) this.selectionHeaderEl.style.display = this.prevSelectionHeaderDisplay;
			if (this.selectionContentEl) this.selectionContentEl.style.display = this.prevSelectionContentDisplay;
		}
	}

	private rebindModalEvents() {
		if (!this.modalContent) return;

		// 重新绑定模式按钮事件
		const modeButtons = this.modalContent.querySelectorAll('.smart-qa-mode-btn');
		modeButtons.forEach(btn => {
			btn.addEventListener('click', () => {
				// 移除所有按钮的active类
				modeButtons.forEach(b => b.classList.remove('smart-qa-mode-btn-active'));
				// 给当前按钮添加active类
				btn.classList.add('smart-qa-mode-btn-active');
				this.updateFileSelection(btn.textContent || '');
			});
		});

	}

	private populateSelectors(folderSelect: HTMLSelectElement, tagContainer: HTMLElement, tagDataList?: HTMLElement, selectedChips?: HTMLElement, tagInput?: HTMLInputElement, sortSelect?: HTMLSelectElement) {
		// 填充文件夹选择器
		const folders = this.fileProcessor.getAllFolders();
		folderSelect.empty();
		folderSelect.createEl('option', { value: '', text: 'Select a folder...' });
		folders.forEach(folder => {
			folderSelect.createEl('option', { value: folder, text: folder });
		});

		// 获取标签与计数
		const tagCounts = this.fileProcessor.getTagCounts();
		let tags: string[] = [];
		const sortMode = sortSelect?.value || 'count-desc';
		if (sortMode.startsWith('count')) {
			const sorted = [...tagCounts].sort((a, b) => sortMode === 'count-desc' ? (b.count - a.count || a.tag.localeCompare(b.tag)) : (a.count - b.count || a.tag.localeCompare(b.tag)));
			tags = sorted.map(x => x.tag);
		} else if (sortMode === 'alpha-asc') {
			tags = [...tagCounts.map(x => x.tag)].sort((a, b) => a.localeCompare(b));
		} else {
			tags = [...tagCounts.map(x => x.tag)].sort((a, b) => b.localeCompare(a));
		}
		this.availableTags = tags;

		// 渲染标签复选
		tagContainer.empty();
		if (tags.length === 0) {
			tagContainer.createEl('div', { text: 'No tags found in your vault', cls: 'smart-qa-no-tags' });
		} else {
			tags.forEach(tag => {
				const tagEl = tagContainer.createEl('div', { cls: 'smart-qa-tag-item' });
				const checkbox = tagEl.createEl('input', { type: 'checkbox' }) as HTMLInputElement;
				checkbox.value = tag;
				checkbox.id = `tag-${tag.replace('#', '')}`;
				tagEl.createEl('label', { text: tag, attr: { for: checkbox.id } });
				checkbox.addEventListener('change', () => {
					this.updateTagSelection();
					this.renderSelectedChips(selectedChips || null);
				});
			});
		}

		// datalist
		if (tagDataList) {
			tagDataList.empty();
			tags.forEach(tag => {
				const option = document.createElement('option');
				option.value = tag;
				(tagDataList as HTMLElement).appendChild(option);
			});
		}

		// 保存引用
		if (selectedChips) this.selectedChipsEl = selectedChips;
		if (tagInput) this.tagInputEl = tagInput;

		// 输入事件
		if (tagInput) {
			tagInput.onkeydown = (e: KeyboardEvent) => {
				if (e.key === 'Enter') {
					e.preventDefault();
					const value = tagInput.value.trim();
					if (!value) return;
					this.selectTagByValue(value);
					tagInput.value = '';
				}
			};
			tagInput.oninput = () => {
				this.filterTagList(tagContainer, tagInput.value.trim());
			};
		}

		// 初始 chips
		this.renderSelectedChips(selectedChips || null);
	}

	// 根据输入值选中/新增标签（仅允许现有标签）
	private selectTagByValue(value: string) {
		// 自动补齐缺失的 #
		let tag = value;
		if (!tag.startsWith('#')) {
			tag = '#' + tag;
		}
		// 只允许选择存在的标签
		if (!this.availableTags.includes(tag)) {
			new Notice('Tag not found in vault');
			return;
		}
		const checkbox = this.fileSelectionContainer.querySelector(`.smart-qa-tag-item input[type="checkbox"][value="${tag}"]`) as HTMLInputElement | null;
		if (checkbox && !checkbox.checked) {
			checkbox.checked = true;
			this.updateTagSelection();
			this.renderSelectedChips(this.selectedChipsEl);
		}
	}

	private toggleSelectionCollapse() {
		this.setSelectionCollapse(!this.isSelectionCollapsed);
	}

	private setSelectionCollapse(collapsed: boolean) {
		this.isSelectionCollapsed = collapsed;
		if (this.selectionContentEl) {
			this.selectionContentEl.style.display = collapsed ? 'none' : 'block';
		}
		if (this.selectionCollapseBtn) {
			setIcon(this.selectionCollapseBtn, collapsed ? 'chevron-down' : 'chevron-up');
			this.selectionCollapseBtn.setAttr('aria-label', collapsed ? 'Expand file selection' : 'Collapse file selection');
		}
	}

	// 过滤标签列表
	private filterTagList(container: HTMLElement, query: string) {
		const items = container.querySelectorAll('.smart-qa-tag-item');
		items.forEach((el) => {
			const label = (el as HTMLElement).innerText.toLowerCase();
			(el as HTMLElement).style.display = query ? (label.includes(query.toLowerCase()) ? '' : 'none') : '';
		});
	}

	// 渲染已选标签 chips
	private renderSelectedChips(wrapper: HTMLElement | null) {
		if (!wrapper) return;
		wrapper.empty();
		const selected = Array.from(this.fileSelectionContainer.querySelectorAll('.smart-qa-tag-item input[type="checkbox"]') as NodeListOf<HTMLInputElement>)
			.filter(cb => cb.checked)
			.map(cb => cb.value);
		selected.forEach(tag => {
			const chip = wrapper.createEl('span', { cls: 'smart-qa-chip' });
			chip.createSpan({ text: tag });
			const btn = chip.createEl('button', { text: '×', attr: { 'aria-label': `Remove ${tag}` } });
			btn.addEventListener('click', () => {
				const cb = this.fileSelectionContainer.querySelector(`.smart-qa-tag-item input[type="checkbox"][value="${tag}"]`) as HTMLInputElement | null;
				if (cb) {
					cb.checked = false;
					this.updateTagSelection();
					this.renderSelectedChips(wrapper);
				}
			});
		});
	}

	private updateTagSelection() {
		const checkboxes = this.fileSelectionContainer.querySelectorAll('.smart-qa-tag-item input[type="checkbox"]:checked') as NodeListOf<HTMLInputElement>;
		const selectedTags = Array.from(checkboxes).map(cb => cb.value);
		
		if (selectedTags.length > 0) {
			this.currentSelection = { mode: 'tags', selection: selectedTags };
			this.processedContent = null;
			this.loadSelectedFiles().then(() => {
				this.updateFileSelectionStatus();
			});
		} else {
			// 如果没有选择标签，显示提示
			const statusElement = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
			if (statusElement) {
				statusElement.textContent = 'Please select at least one tag';
			}
		}
	}

	private openSettings() {
		// @ts-ignore
		this.app.setting.open();
		// @ts-ignore
		this.app.setting.openTabById(this.plugin.id);
	}

	private async updateFileSelection(mode: string) {
		// 更新选择模式
		switch (mode) {
			case 'All Files':
				this.currentSelection = { mode: 'all' };
				// 清除当前处理的内容，强制重新加载
				this.processedContent = null;
				this.updateStatus('Loading...', 'Scanning all files...');
				await this.loadSelectedFiles();
				this.updateStatus('Ready', this.processedContent?.summary || 'Ready');
				break;
				
			case 'Folder':
				this.currentSelection = { mode: 'folder' };
				const statusElement1 = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
				if (statusElement1) {
					statusElement1.textContent = 'Select a folder to include';
				}
				break;
				
			case 'Tags':
				this.currentSelection = { mode: 'tags' };
				const statusElement2 = this.fileSelectionContainer.querySelector('.smart-qa-selection-status');
				if (statusElement2) {
					statusElement2.textContent = 'Select tags to filter files';
				}
				break;
		}
	}

	private updateTokenCount(text: string, element: HTMLElement) {
		// 使用更精确的 token 估算
		const estimatedTokens = this.fileProcessor.estimateTokens(text);
		element.textContent = estimatedTokens.toString();
	}

	private updateStatus(status: string, details: string) {
		this.statusContainer.textContent = `${status} | ${details}`;
	}

	private async addMessage(message: { type: 'user' | 'assistant' | 'system'; content: string; timestamp: Date }) {
		const messageEl = this.messagesContainer.createEl('div', { cls: `smart-qa-message smart-qa-message-${message.type}` });
		const contentEl = messageEl.createEl('div', { cls: 'smart-qa-message-content' });

		// 使用 Obsidian 的 MarkdownRenderer 渲染富文本
		if (message.type === 'assistant' || message.type === 'system') {
			contentEl.empty();
			await this.renderMarkdownTo(contentEl, message.content);
		} else {
			contentEl.setText(message.content);
		}

		// 为助手消息添加操作按钮
		if (message.type === 'assistant') {
			this.addMessageActions(contentEl, message.content);
		}

		// 滚动到底部
		this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
	}

		private addMessageActions(contentEl: HTMLElement, content: string) {
			contentEl.addClass('smart-qa-message-content-with-actions');
			const actionsEl = contentEl.createEl('div', { cls: 'smart-qa-message-actions' });
		
		// 复制按钮（线性图标）
		const copyBtn = actionsEl.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-btn-icon-action',
			title: '复制回答'
		});
		copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>`;
		
		copyBtn.addEventListener('click', async () => {
			await navigator.clipboard.writeText(content);
			// 临时显示复制成功的图标
			copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>`;
			copyBtn.title = '已复制！';
			setTimeout(() => {
				copyBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>`;
				copyBtn.title = '复制回答';
			}, 2000);
		});

		// 新建笔记按钮（线性图标）
		const createNoteBtn = actionsEl.createEl('button', { 
			cls: 'smart-qa-btn smart-qa-btn-icon-action',
			title: '新建笔记'
		});
		createNoteBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/><line x1="12" y1="18" x2="12" y2="12"/><line x1="9" y1="15" x2="15" y2="15"/></svg>`;
		
		createNoteBtn.addEventListener('click', () => {
			this.createNoteFromResponse(content);
		});
	}

	private async createNoteFromResponse(content: string) {
		try {
			const folder = this.plugin.settings.notesFolder;
			const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
			const filename = `AI回答-${timestamp}.md`;
			
			// 确保文件夹存在
			if (folder && !this.app.vault.getAbstractFileByPath(folder)) {
				await this.app.vault.createFolder(folder);
			}
			
			const filePath = folder ? `${folder}/${filename}` : filename;
			
			// 创建笔记内容
			const noteContent = `# AI 回答

创建时间：${new Date().toLocaleString('zh-CN')}

---

${content}

---

*此笔记由 Smart QA 插件自动生成*`;

			const file = await this.app.vault.create(filePath, noteContent);
			
			// 询问是否打开笔记
			setTimeout(() => {
				const openNote = confirm('笔记已创建！是否要打开这个笔记？');
				if (openNote) {
					this.app.workspace.getLeaf().openFile(file);
				}
			}, 100);
			
		} catch (error) {
			console.error('创建笔记失败:', error);
			alert(`创建笔记失败: ${error.message}`);
		}
	}

	private async handleUserMessage(message: string) {
		console.log('handleUserMessage called with:', message);
		
		if (this.isProcessing) {
			console.log('Already processing, ignoring message');
			return;
		}
		
		// 检查 API 密钥
		if (!this.plugin.settings.openRouterApiKey && !this.plugin.settings.googleAiApiKey) {
			console.log('No API keys configured');
			this.addMessage({
				type: 'system',
				content: 'Please configure your API keys in settings before using Smart QA.',
				timestamp: new Date()
			});
			return;
		}

		this.isProcessing = true;

		// 添加用户消息到历史记录和对话管理器
		const userMessage: AIMessage = { role: 'user', content: message };
		this.conversationHistory.push(userMessage);

		const userConvMessage: ConversationMessage = {
			type: 'user',
			content: message,
			timestamp: new Date()
		};

		if (this.currentConversationId) {
			this.conversationManager.addMessage(this.currentConversationId, userConvMessage);
			
			// 更新对话的文件选择和模型信息
			this.conversationManager.updateConversation(this.currentConversationId, {
				fileSelection: this.currentSelection,
				model: this.currentModelId
			});
		}

		// 显示用户消息
		this.addMessage({
			type: 'user',
			content: message,
			timestamp: new Date()
		});

		// 创建助手加载占位气泡
		const loadingMessageEl = this.messagesContainer.createEl('div', {
			cls: 'smart-qa-message smart-qa-message-assistant smart-qa-message-loading'
		});
		const loadingContentEl = loadingMessageEl.createEl('div', {
			cls: 'smart-qa-message-content smart-qa-message-content-loading'
		});
		const dotsWrapper = loadingContentEl.createEl('span', {
			cls: 'smart-qa-loading-dots',
			attr: { 'aria-label': 'Loading response' }
		});
		for (let i = 0; i < 3; i++) {
			dotsWrapper.createEl('span');
		}
		this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;

		let assistantMessageEl: HTMLElement | null = loadingMessageEl;
		let assistantContentEl: HTMLElement | null = loadingContentEl;
		let loadingActive = true;

		const ensureAssistantMessage = () => {
			if (!assistantMessageEl || !assistantContentEl) {
				assistantMessageEl = this.messagesContainer.createEl('div', { cls: 'smart-qa-message smart-qa-message-assistant' });
				assistantContentEl = assistantMessageEl.createEl('div', { cls: 'smart-qa-message-content' });
			}
			return assistantContentEl;
		};

		const stopLoadingPlaceholder = () => {
			if (loadingActive && assistantMessageEl && assistantContentEl) {
				assistantMessageEl.classList.remove('smart-qa-message-loading');
				assistantContentEl.classList.remove('smart-qa-message-content-loading');
				assistantContentEl.empty();
				loadingActive = false;
			}
		};

		const removeLoadingPlaceholder = () => {
			loadingActive = false;
			if (assistantMessageEl && assistantMessageEl.isConnected) {
				assistantMessageEl.remove();
			}
			assistantMessageEl = null;
			assistantContentEl = null;
		};

		// 更新状态
		this.updateStatus('Processing...', 'Getting AI response...');

		try {
			// 如果还没有处理文件内容或选择发生变化，先处理文件
			if (!this.processedContent) {
				this.updateStatus('Processing...', 'Loading files...');
				await this.loadSelectedFiles();
			}

			// 准备包含知识库内容的系统提示
			let systemPrompt = 'You are Smart QA, an AI assistant helping users analyze their Obsidian knowledge base. Provide clear, helpful answers based on the context provided. If you reference specific information, mention which files it comes from.\\n\\n';
			
			if (this.processedContent && this.processedContent.files.length > 0) {
				const contextContent = this.fileProcessor.formatForAI(this.processedContent);
				systemPrompt += contextContent;
			} else {
				systemPrompt += 'No files are currently selected or accessible.';
			}

			// 准备消息
			const messages: AIMessage[] = [
				{
					role: 'system',
					content: systemPrompt
				},
				...this.conversationHistory
			];

			// 使用当前选择的模型
			const model = this.currentModelId;
			if (!model) {
				this.addMessage({
					type: 'system',
					content: 'Please select a model first.',
					timestamp: new Date()
				});
				return;
			}

			// 获取模型信息
			const modelInfo = getModelInfo(this.plugin.settings, model);
			if (!modelInfo) {
				this.addMessage({
					type: 'system',
					content: 'Selected model not found. Please check your settings.',
					timestamp: new Date()
				});
				return;
			}

			// 构建完整的模型ID
			const fullModelId = `${modelInfo.provider}/${modelInfo.modelId}`;
			
			let fullResponse = '';

			// 流式响应
			for await (const chunk of this.aiService.streamChat(messages, fullModelId)) {
				if (chunk.error) {
					this.addMessage({
						type: 'system',
						content: `Error: ${chunk.error}`,
						timestamp: new Date()
					});
					break;
				}

				// 更新内容（Markdown 渲染）
				if (chunk.content) {
					stopLoadingPlaceholder();
					const targetEl = ensureAssistantMessage();
					fullResponse += chunk.content;
					targetEl.empty();
					await this.renderMarkdownTo(targetEl, fullResponse);
					// 滚动到底部
					this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
				}

				if (chunk.done) {
					break;
				}
			}

			// 为流式响应添加操作按钮
			if (fullResponse && assistantContentEl) {
				this.addMessageActions(assistantContentEl, fullResponse);
			}

			// 如果流式没有返回内容，进行一次非流式回退
			if (!fullResponse) {
				const fallback = await this.aiService.chat(messages, fullModelId, false);
				if (fallback.error) {
					removeLoadingPlaceholder();
					this.addMessage({
						type: 'system',
						content: `Error: ${fallback.error}`,
						timestamp: new Date()
					});
				} else if (fallback.content) {
					fullResponse = fallback.content;
					const targetEl = ensureAssistantMessage();
					stopLoadingPlaceholder();
					targetEl.empty();
					await this.renderMarkdownTo(targetEl, fullResponse);
					// 为回退响应添加操作按钮
					this.addMessageActions(targetEl, fullResponse);
				}
			}

			// 添加助手消息到历史记录和对话管理器
			if (fullResponse) {
				const assistantAIMessage: AIMessage = {
					role: 'assistant',
					content: fullResponse
				};
				this.conversationHistory.push(assistantAIMessage);

				const assistantConvMessage: ConversationMessage = {
					type: 'assistant',
					content: fullResponse,
					timestamp: new Date()
				};

				if (this.currentConversationId) {
					this.conversationManager.addMessage(this.currentConversationId, assistantConvMessage);
				}
			} else {
				// 仍然没有任何内容，给出提示
				removeLoadingPlaceholder();
				this.addMessage({
					type: 'system',
					content: 'No response received from the model. Please verify API key and model settings.',
					timestamp: new Date()
				});
			}

			// 更新状态
			const tokenCount = this.aiService.estimateTokens(
				messages.map(m => m.content).join(''), 
				fullModelId
			);
			this.updateStatus('Ready', `${this.getStatusText()} | Tokens: ~${tokenCount}`);

		} catch (error) {
			removeLoadingPlaceholder();
			this.addMessage({
				type: 'system',
				content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
				timestamp: new Date()
			});
			this.updateStatus('Ready', 'Error occurred');
		} finally {
			if (loadingActive) {
				removeLoadingPlaceholder();
			}
			this.isProcessing = false;
		}
	}
}
